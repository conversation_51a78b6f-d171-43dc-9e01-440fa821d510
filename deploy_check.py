#!/usr/bin/env python3
"""
Final deployment check for MMMUT chatbot
"""

import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and return success status"""
    try:
        print(f"Running: {description}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True, cwd=Path.cwd())
        if result.returncode == 0:
            print(f"✓ {description} - SUCCESS")
            return True
        else:
            print(f"✗ {description} - FAILED")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ {description} - ERROR: {str(e)}")
        return False

def main():
    """Main deployment check"""
    print("MMMUT Chatbot - Final Deployment Check")
    print("=" * 50)
    
    checks = [
        ("python3 status_check.py", "System Status Check"),
        ("python3 -c 'import src.chatbot; print(\"Chatbot import OK\")'", "Chatbot Import Test"),
        ("python3 -c 'import src.integration; print(\"Integration import OK\")'", "Integration Import Test"),
        ("python3 test_chatbot.py", "Chatbot Functionality Test")
    ]
    
    passed = 0
    total = len(checks)
    
    for command, description in checks:
        if run_command(command, description):
            passed += 1
        print()
    
    print("=" * 50)
    print(f"DEPLOYMENT CHECK RESULTS: {passed}/{total} PASSED")
    
    if passed == total:
        print("🎉 ALL CHECKS PASSED - READY FOR DEPLOYMENT!")
        print("\nNext steps:")
        print("1. git add .")
        print("2. git commit -m 'Complete MMMUT chatbot implementation'")
        print("3. git push origin main")
        print("\nTo run the chatbot:")
        print("- CLI: python3 run_chatbot.py")
        print("- Web: python3 run_web.py")
        return True
    else:
        print("⚠️ SOME CHECKS FAILED - PLEASE FIX BEFORE DEPLOYMENT")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)