#!/usr/bin/env python3
"""
Quick demo of MMMUT chatbot functionality
"""

import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def quick_demo():
    """Run a quick demo of the chatbot"""
    print("🎓 MMMUT Admission Chatbot - Quick Demo")
    print("=" * 50)
    
    try:
        from chatbot import AdmissionChatbot
        
        # Initialize chatbot
        print("Initializing chatbot...")
        chatbot = AdmissionChatbot()
        print("✓ Chatbot ready!\n")
        
        # Demo queries with expected responses
        demo_queries = [
            "Hello! I want to know about MMMUT admission",
            "What courses are offered?",
            "What is the fee structure?",
            "What are the eligibility criteria?",
            "Tell me about placement statistics",
            "How can I contact the admission office?"
        ]
        
        print("Demo Conversation:")
        print("-" * 30)
        
        for i, query in enumerate(demo_queries, 1):
            print(f"\n👤 Student: {query}")
            
            response = chatbot.process_query(query)
            
            # Get response text
            if isinstance(response['response'], list):
                response_text = response['response'][0]
            else:
                response_text = response['response']
            
            # Truncate long responses for demo
            if len(response_text) > 150:
                response_text = response_text[:150] + "..."
            
            print(f"🤖 MMMUT Bot: {response_text}")
            print(f"   [Type: {response['response_type']}, Confidence: {response['confidence']:.2f}]")
        
        # Show statistics
        stats = chatbot.get_statistics()
        print(f"\n" + "=" * 50)
        print("Session Statistics:")
        print(f"• Total queries processed: {stats['total_queries']}")
        print(f"• Data categories available: {stats['data_categories']}")
        print(f"• FAQ entries: {stats['available_faqs']}")
        print(f"• Session duration: {stats['session_duration']}")
        
        print(f"\n✅ Demo completed successfully!")
        print("\nThe chatbot can handle queries about:")
        print("• University information and location")
        print("• Course details and specializations")
        print("• Admission eligibility and process")
        print("• Fee structure and scholarships")
        print("• Campus facilities and infrastructure")
        print("• Placement statistics and companies")
        print("• Contact information")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = quick_demo()
    sys.exit(0 if success else 1)