#!/usr/bin/env python3
"""
Demo script for MMMUT chatbot
"""

import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def demo():
    """Run a demo of the chatbot"""
    try:
        print("🎓 MMMUT Admission Chatbot Demo")
        print("=" * 50)
        
        from chatbot import AdmissionChatbot
        
        # Initialize chatbot
        print("Initializing chatbot...")
        chatbot = AdmissionChatbot()
        print("✓ Chatbot ready!\n")
        
        # Demo queries
        demo_queries = [
            "Hello! I'm interested in MMMUT admission",
            "What courses are offered?",
            "What is the fee structure for B.Tech?",
            "What are the eligibility criteria?",
            "Tell me about placement statistics",
            "How can I contact the admission office?"
        ]
        
        print("Demo Conversation:")
        print("-" * 30)
        
        for i, query in enumerate(demo_queries, 1):
            print(f"\n👤 Student: {query}")
            
            response = chatbot.process_query(query)
            
            # Get response text
            if isinstance(response['response'], list):
                response_text = response['response'][0]
            else:
                response_text = response['response']
            
            print(f"🤖 MMMUT Bot: {response_text}")
            
            if i < len(demo_queries):
                input("   Press Enter to continue...")
        
        print("\n" + "=" * 50)
        print("✅ Demo completed successfully!")
        print("\nThe chatbot can handle queries about:")
        print("• University information")
        print("• Course details and specializations")
        print("• Admission eligibility and process")
        print("• Fee structure and scholarships")
        print("• Campus facilities")
        print("• Placement statistics")
        print("• Contact information")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = demo()
    sys.exit(0 if success else 1)